plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.jetbrains.kotlin.android)
    alias(libs.plugins.sqldelight)
    id ("com.google.dagger.hilt.android")
    id ("kotlin-kapt")
    id ("kotlin-parcelize")
    id ("org.jetbrains.kotlin.plugin.serialization") version "1.9.25"
    id("com.google.gms.google-services")
    alias(libs.plugins.compose.compiler)
    id("com.google.firebase.firebase-perf")
    id("com.google.firebase.crashlytics")
}

android {
    namespace = "com.thedasagroup.suminative"
    compileSdk = 34
    flavorDimensions( "default")

    signingConfigs {
        create("release") {
            keyAlias = "dasaaliaskeyxyz"
            keyPassword = "@SysDasa1991"
            storeFile = file("dasa.jks")
            storePassword = "@SysDasa1991"
        }
    }

    defaultConfig {
        applicationId = "com.dasadirect.dasapos"
        minSdk = 26
        targetSdk = 34
        versionCode = 112
        versionName = "2.36"
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
        externalNativeBuild {
            cmake {
                cppFlags += "-DAPI_TOKEN=\\\\\\\"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiLRMdt/fzTOaQPf8LK6VTNOlb1qMFXUBrIGDlKgXbK3hjAyLXcawAZp+jDsk81e8CjVOUSm/zTqQGU98Xr6ZAu4o+3RD1cPNph9D/EwJy4QGwjWWevp/MpRmxIbS33bYlBt3W64tmM38raRKd10j3YtgBrVX4kmFns4I955Fk/y1daK6p8VcyzCSqKvO3sjkIhzFSGfDfoLn2+7rkmoZ37Lb/kE6U8YYkd2UNcpWqbg+H7unTr9VKynYLxfU3aBfbNYtxXBmidRB49JlLBqueOAI2hma9w0cA96a/pB/k3MKGzaCMvTq2oxXehh5bjrIZgeCWLG20ID+LRi6SixJswIDAQAB\\\\\\\""
            }
        }
        manifestPlaceholders["appAuthRedirectScheme"] = "com.dasadirect.dasapos2"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
            signingConfig = signingConfigs.getByName("release")
        }
    }
    buildFeatures {
        buildConfig = true
        compose = true
        aidl = true
    }
    
    // Fix AIDL compilation issues
    androidResources {
        noCompress += listOf("aidl")
    }
    
    // Filter out problematic SumUp SDK resources
    packagingOptions {
        resources {
            excludes += listOf(
                "**/values-*/strings.xml",
                "**/values/strings.xml"
            )
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = "1.8"
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.15"
    }
    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
    val domainStaging = "\"dasasplace.com\""
    val domainProd = "\"dasadirect.com\""
    val APITOKEN = "\"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiLRMdt/fzTOaQPf8LK6VTNOlb1qMFXUBrIGDlKgXbK3hjAyLXcawAZp+jDsk81e8CjVOUSm/zTqQGU98Xr6ZAu4o+3RD1cPNph9D/EwJy4QGwjWWevp/MpRmxIbS33bYlBt3W64tmM38raRKd10j3YtgBrVX4kmFns4I955Fk/y1daK6p8VcyzCSqKvO3sjkIhzFSGfDfoLn2+7rkmoZ37Lb/kE6U8YYkd2UNcpWqbg+H7unTr9VKynYLxfU3aBfbNYtxXBmidRB49JlLBqueOAI2hma9w0cA96a/pB/k3MKGzaCMvTq2oxXehh5bjrIZgeCWLG20ID+LRi6SixJswIDAQAB\""
    productFlavors {
        create("stagingSumni") {
            buildConfigField("String","DOMAIN_NAME", domainStaging)
            buildConfigField("String","SHOW_PRINTBILL","\"true\"")
            buildConfigField("String","USES_PRINTER_X","\"\"")
            buildConfigField("String","APITOKEN", APITOKEN)
        }
        create("stagingGeneral") {
            buildConfigField("String","DOMAIN_NAME",domainStaging)
            buildConfigField("String","SHOW_PRINTBILL","\"\"")
            buildConfigField("String","USES_PRINTER_X","\"\"")
            buildConfigField("String","APITOKEN", APITOKEN)
        }
        create("prodSumni") {
            buildConfigField("String","DOMAIN_NAME",domainProd)
            buildConfigField("String","SHOW_PRINTBILL","\"true\"")
            buildConfigField("String","USES_PRINTER_X","\"\"")
            buildConfigField("String","APITOKEN", APITOKEN)
        }
        create("prodGeneral") {
            buildConfigField("String","DOMAIN_NAME",domainProd)
            buildConfigField("String","SHOW_PRINTBILL","\"\"")
            buildConfigField("String","USES_PRINTER_X","\"\"")
            buildConfigField("String","APITOKEN", APITOKEN)
        }
        create("prodSumniPos2") {
            applicationId = "com.dasadirect.dasapos2"
            buildConfigField("String","DOMAIN_NAME",domainProd)
            buildConfigField("String","SHOW_PRINTBILL","\"true\"")
            buildConfigField("String","USES_PRINTER_X","\"\"")
            buildConfigField("String","APITOKEN", APITOKEN)
        }
        create("prodSumniD3Pro") {
            applicationId = "com.thedasagroup.d3pro"
            buildConfigField("String","DOMAIN_NAME",domainProd)
            buildConfigField("String","SHOW_PRINTBILL","\"true\"")
            buildConfigField("String","USES_PRINTER_X","\"\"")
            buildConfigField("String","APITOKEN", APITOKEN)
        }
        create("stagingSumniPos2") {
            applicationId = "com.dasadirect.dasapos2"
            buildConfigField("String","DOMAIN_NAME",domainStaging)
            buildConfigField("String","SHOW_PRINTBILL","\"true\"")
            buildConfigField("String","USES_PRINTER_X","\"true\"")
            buildConfigField("String","APITOKEN", APITOKEN)
        }
        create("prodSumniPos3") {
            applicationId = "com.dasadirect.dasapos3"
            buildConfigField("String","DOMAIN_NAME",domainProd)
            buildConfigField("String","SHOW_PRINTBILL","\"true\"")
            buildConfigField("String","USES_PRINTER_X","\"true\"")
            buildConfigField("String","APITOKEN", APITOKEN)
        }
        create("stagingGeneralMobilePOS") {
            applicationId = "com.thedasagroup.mpos"
            buildConfigField("String","DOMAIN_NAME",domainStaging)
            buildConfigField("String","SHOW_PRINTBILL","\"\"")
            buildConfigField("String","USES_PRINTER_X","\"\"")
            buildConfigField("String","APITOKEN", APITOKEN)
        }
        create("prodGeneralMobilePOS") {
            applicationId = "com.thedasagroup.mpos"
            buildConfigField("String","DOMAIN_NAME",domainProd)
            buildConfigField("String","SHOW_PRINTBILL","\"\"")
            buildConfigField("String","USES_PRINTER_X","\"\"")
            buildConfigField("String","APITOKEN", APITOKEN)
        }
        create("stagingSumniMobilePOS") {
            applicationId = "com.thedasagroup.mpos"
            buildConfigField("String","DOMAIN_NAME",domainStaging)
            buildConfigField("String","SHOW_PRINTBILL","\"true\"")
            buildConfigField("String","USES_PRINTER_X","\"\"")
            buildConfigField("String","APITOKEN", APITOKEN)
        }
        create("prodSumniMobilePOS") {
            applicationId = "com.thedasagroup.mpos"
            buildConfigField("String","DOMAIN_NAME",domainProd)
            buildConfigField("String","SHOW_PRINTBILL","\"true\"")
            buildConfigField("String","USES_PRINTER_X","\"\"")
            buildConfigField("String","APITOKEN", APITOKEN)
        }
    }
    sourceSets {
        named("main") {
            aidl.srcDir("../buildSrc/src/main/aidl")
        }
        //Tablet POS
        named("stagingSumni") {
            java.srcDirs("src/main/java", "src/tabletpos/java")
            res.srcDirs("src/main/res", "src/tabletpos/res")
            assets.srcDirs("src/main/assets", "src/tabletpos/assets")
            aidl.srcDirs("src/main/aidl", "src/tabletpos/aidl")
            manifest.srcFile("src/tabletpos/AndroidManifest.xml")
        }
        named("stagingGeneral") {
            java.srcDirs("src/main/java", "src/tabletpos/java")
            res.srcDirs("src/main/res", "src/tabletpos/res")
            assets.srcDirs("src/main/assets", "src/tabletpos/assets")
            aidl.srcDirs("src/main/aidl", "src/tabletpos/aidl")
            manifest.srcFile("src/tabletpos/AndroidManifest.xml")
        }

        named("stagingSumniPos2") {
            java.srcDirs("src/main/java", "src/tabletpos/java")
            res.srcDirs("src/main/res", "src/tabletpos/res")
            assets.srcDirs("src/main/assets", "src/tabletpos/assets")
            aidl.srcDirs("src/main/aidl", "src/tabletpos/aidl")
            manifest.srcFile("src/tabletpos/AndroidManifest.xml")
        }
        named("prodSumniPos2") {
            java.srcDirs("src/main/java", "src/tabletpos/java")
            res.srcDirs("src/main/res", "src/tabletpos/res")
            assets.srcDirs("src/main/assets", "src/tabletpos/assets")
            aidl.srcDirs("src/main/aidl", "src/tabletpos/aidl")
            manifest.srcFile("src/tabletpos/AndroidManifest.xml")
        }
        named("prodSumniD3Pro") {
            java.srcDirs("src/main/java", "src/tabletpos/java")
            res.srcDirs("src/main/res", "src/d3pro/res")
            assets.srcDirs("src/main/assets", "src/tabletpos/assets")
            aidl.srcDirs("src/main/aidl", "src/tabletpos/aidl")
            manifest.srcFile("src/tabletpos/AndroidManifest.xml")
        }
        named("prodGeneral") {
            java.srcDirs("src/main/java", "src/tabletpos/java")
            res.srcDirs("src/main/res", "src/tabletpos/res")
            assets.srcDirs("src/main/assets", "src/tabletpos/assets")
            aidl.srcDirs("src/main/aidl", "src/tabletpos/aidl")
            manifest.srcFile("src/tabletpos/AndroidManifest.xml")
        }

        //Mobile POS
        named("stagingGeneralMobilePOS") {
            java.srcDirs("src/main/java", "src/mobilepos/java")
            res.srcDirs("src/main/res", "src/mobilepos/res")
            assets.srcDirs("src/main/assets", "src/mobilepos/assets")
            aidl.srcDirs("src/main/aidl", "src/mobilepos/aidl")
            manifest.srcFile("src/mobilepos/AndroidManifest.xml")
        }
        named("prodGeneralMobilePOS") {
            java.srcDirs("src/main/java", "src/mobilepos/java")
            res.srcDirs("src/main/res", "src/mobilepos/res")
            assets.srcDirs("src/main/assets", "src/mobilepos/assets")
            aidl.srcDirs("src/main/aidl", "src/mobilepos/aidl")
            manifest.srcFile("src/mobilepos/AndroidManifest.xml")
        }

        named("stagingSumniMobilePOS") {
            java.srcDirs("src/main/java", "src/mobilepos/java")
            res.srcDirs("src/main/res", "src/mobilepos/res")
            assets.srcDirs("src/main/assets", "src/mobilepos/assets")
            aidl.srcDirs("src/main/aidl", "src/mobilepos/aidl")
            manifest.srcFile("src/mobilepos/AndroidManifest.xml")
        }
        named("prodSumniMobilePOS") {
            java.srcDirs("src/main/java", "src/mobilepos/java")
            res.srcDirs("src/main/res", "src/mobilepos/res")
            assets.srcDirs("src/main/assets", "src/mobilepos/assets")
            aidl.srcDirs("src/main/aidl", "src/mobilepos/aidl")
            manifest.srcFile("src/mobilepos/AndroidManifest.xml")
        }
    }
    externalNativeBuild {
        cmake {
            path = file("src/main/cpp/CMakeLists.txt")
            version = "3.22.1"
        }
    }
    ndkVersion = "29.0.13846066 rc3"
    buildToolsVersion = "36.0.0"
}

sqldelight {
    databases {
        create("POSDatabase") {
            packageName.set("com.thedasagroup.suminative.database")
            // Try explicitly naming the argument
//            sourceFolders.set(value = listOf("sqldelight"))
        }
    }
}

dependencies {
    val plutoVersion = "2.2.1"
    val ktorVersion = "3.2.1"
    val nav_version = "2.8.2"

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation(libs.material)
    implementation(libs.androidx.work.runtime.ktx)
    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)

    implementation ("androidx.hilt:hilt-work:1.0.0")
    kapt ("androidx.hilt:hilt-compiler:1.0.0")

    implementation("androidx.appcompat:appcompat:1.7.0")

    implementation ("com.airbnb.android:mavericks:3.0.10")
    implementation ("com.airbnb.android:mavericks-hilt:3.0.10")
    implementation ("com.airbnb.android:mavericks-compose:3.0.10")
    implementation("com.airbnb.android:mavericks-mocking:3.0.10")
    implementation("com.airbnb.android:mavericks-launcher:3.0.10")

    implementation ("com.github.Kaaveh:sdp-compose:1.1.0")

    implementation(platform("com.google.firebase:firebase-bom:33.0.0"))
    implementation("com.google.firebase:firebase-config")
    implementation("com.google.firebase:firebase-perf")
    implementation("com.google.firebase:firebase-crashlytics")
    implementation("com.google.firebase:firebase-analytics")

    implementation(platform("io.ktor:ktor-bom:${ktorVersion}"))
    implementation("io.ktor:ktor-client-android")
    implementation("io.ktor:ktor-client-serialization")
    implementation("io.ktor:ktor-client-logging")
    implementation("io.ktor:ktor-client-content-negotiation")
    implementation("io.ktor:ktor-serialization-kotlinx-json")

    implementation("io.coil-kt:coil-compose:2.6.0")
    implementation ("androidx.compose.material:material-icons-extended:1.3.1")

    implementation ("com.google.dagger:hilt-android:2.56.2")
    kapt("com.google.dagger:hilt-compiler:2.56.2")

    implementation("com.google.accompanist:accompanist-swiperefresh:0.32.0")

    implementation ("org.jetbrains.kotlinx:kotlinx-serialization-json:1.4.1")
    implementation ("com.sunmi:printerlibrary:latest.release")
//    implementation ("com.sunmi:printerx:1.0.17")
    implementation ("org.java-websocket:Java-WebSocket:1.4.0")
    implementation(platform("com.google.firebase:firebase-bom:33.1.1"))
    implementation("com.google.firebase:firebase-analytics")

    implementation ("com.afollestad.material-dialogs:core:3.3.0")
    implementation ("com.github.kittinunf.fuel:fuel:2.1.0")
    implementation ("com.github.kittinunf.fuel:fuel-android:2.1.0")

    implementation ("dev.shreyaspatil:capturable:2.1.0")

    debugImplementation ("com.plutolib:pluto:$plutoVersion")
    releaseImplementation ("com.plutolib:pluto-no-op:$plutoVersion")

    debugImplementation ("com.plutolib.plugins:network-interceptor-ktor:$plutoVersion")
    releaseImplementation ("com.plutolib.plugins:network-interceptor-ktor-no-op:$plutoVersion")

    debugImplementation ("com.plutolib.plugins:network:$plutoVersion")
    releaseImplementation ("com.plutolib.plugins:network-no-op:$plutoVersion")

    // SQLDelight
    implementation(libs.sqldelight.android.driver)
    implementation(libs.sqldelight.coroutines.extensions)

    implementation("androidx.webkit:webkit:1.8.0")

    implementation ("com.github.instacart:truetime-android:4.0.0.alpha")
    implementation("androidx.navigation:navigation-compose:$nav_version")
    implementation("androidx.compose.material:material:1.7.1")
    implementation ("com.sunmi:printerx:latest.release")

    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.8.3") // or a later version
    implementation("androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3") // or a later version
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.1")

    implementation ("com.sunmi:printerx:1.0.17")
    implementation("com.squareup.retrofit2:retrofit:2.11.0")
    implementation("com.jakewharton.retrofit:retrofit2-kotlinx-serialization-converter:1.0.0")
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")

    implementation("com.jakewharton.timber:timber:5.0.1")
    implementation("com.github.markowanga:timber-logging-to-file:1.3.0")
    implementation("androidx.work:work-runtime-ktx:2.5.0")

    // SumUp SDK (Needs UK-London VPN to sync)
    implementation("net.openid:appauth:0.11.1")
    implementation("com.sumup:merchant-sdk:5.0.3")

    implementation("com.orhanobut:logger:2.2.0")
}
