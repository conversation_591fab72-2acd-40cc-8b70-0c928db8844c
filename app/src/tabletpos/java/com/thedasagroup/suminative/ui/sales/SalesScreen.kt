package com.thedasagroup.suminative.ui.sales

import android.graphics.Bitmap
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Card
import androidx.compose.material.LocalTextStyle
import androidx.compose.material.MaterialTheme
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Divider
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.navigation.compose.rememberNavController
import com.airbnb.mvrx.Loading
import com.airbnb.mvrx.Success
import com.airbnb.mvrx.compose.collectAsState
import com.thedasagroup.suminative.data.model.request.sales.SalesRequest
import com.thedasagroup.suminative.data.model.response.sales.SalesReportResponse
import com.thedasagroup.suminative.data.prefs.Prefs
import com.thedasagroup.suminative.ui.common.customComposableViews.DateRange
import com.thedasagroup.suminative.ui.common.customComposableViews.DateRangeDropdown
import com.thedasagroup.suminative.ui.orders.screenshotableComposable
import com.thedasagroup.suminative.ui.print.DateComposable
import com.thedasagroup.suminative.ui.print.MyTextDivider
import com.thedasagroup.suminative.ui.print.Total
import com.thedasagroup.suminative.ui.print.verticalScrollbar
import com.thedasagroup.suminative.ui.products.ProductsScreenState
import com.thedasagroup.suminative.ui.products.ProductsScreenViewModel
import com.thedasagroup.suminative.ui.theme.SumiNativeTheme
import com.thedasagroup.suminative.ui.utils.DATE_FORMAT_DATE_ONLY
import com.thedasagroup.suminative.ui.utils.formatDate
import com.thedasagroup.suminative.ui.utils.transformDecimal
import ir.kaaveh.sdpcompose.sdp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SalesScreenMain(
    onBackClick: () -> Unit, viewModel: ProductsScreenViewModel,
    onPrintBill: (Bitmap, SalesRequest) -> Unit
) {
    // Ensure we get total sales first to properly display them
    val salesRequest = SalesRequest(
        storeId = viewModel.prefs.store?.id ?: 0,
        startDate = viewModel.trueTimeImpl.now().formatDate(DATE_FORMAT_DATE_ONLY),
        endDate = viewModel.trueTimeImpl.now().formatDate(DATE_FORMAT_DATE_ONLY),
    )

    LaunchedEffect(key1 = "getSales") {
        withContext(Dispatchers.IO) {
            viewModel.getTotalSales(request = salesRequest)
            // Then get the sales report with the same request
            viewModel.getSalesReport(request = salesRequest)
        }
    }

    val showSaleReportDialog by viewModel.collectAsState(ProductsScreenState::showSalesReportDialog)
    val coroutineScope = rememberCoroutineScope()

    if (showSaleReportDialog) {
        SalesReportDialog(
            modifier = Modifier,
            onPrintBill = {
                onPrintBill(it, salesRequest)
            },
            onCancel = {
                viewModel.showSalesReportDialog(show = false)
            },
            productsScreenViewModel = viewModel
        )
    }

    SumiNativeTheme {
        val navController = rememberNavController()
        val coroutineScope = rememberCoroutineScope()
        Scaffold(modifier = Modifier, topBar = {
            TopAppBar(title = {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(onClick = {
                        onBackClick()
                    }) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack, contentDescription = "Back"
                        )
                    }
                    Spacer(modifier = Modifier.size(8.sdp))
                    androidx.compose.material.Text(
                        text = "Sales",
                        style = androidx.compose.material3.MaterialTheme.typography.bodyLarge
                    )
                }
            })
        }) { innerPadding ->
            SalesScreen(viewModel = viewModel, onSalesReportClick = {
                viewModel.showSalesReportDialog(show = true)
            }, updateSalesReport = { startDate, endDate ->
                coroutineScope.launch(Dispatchers.IO) {
                    viewModel.getTotalSales(
                        request = SalesRequest(
                            storeId = viewModel.prefs.store?.id ?: 0,
                            startDate = startDate,
                            endDate = endDate,
                        )
                    )
                    viewModel.getSalesReport(
                        request = SalesRequest(
                            storeId = viewModel.prefs.store?.id ?: 0,
                            startDate = startDate,
                            endDate = endDate,
                        )
                    )
                }
            })
        }
    }
}

@Composable
fun SalesScreen(
    viewModel: ProductsScreenViewModel,
    onSalesReportClick: () -> Unit,
    updateSalesReport: (String, String) -> Unit
) {
    val salesResponse by viewModel.collectAsState(ProductsScreenState::salesResponse)
    
    // Default values in case data is loading or empty
    val totalSales = when (salesResponse) {
        is Loading -> "Loading..."
        is Success -> (salesResponse())?.numberOfSales?.toString() ?: "0"
        else -> "0"
    }
    
    val totalAmount = when (salesResponse) {
        is Loading -> "Loading..."
        is Success -> (salesResponse())?.totalAmount?.transformDecimal() ?: "0.00"
        else -> "0.00"
    }

    // Get salesRequest if available
    val salesRequest by viewModel.collectAsState(ProductsScreenState::salesRequest)
    var selectedDateRange by remember { mutableStateOf(DateRange.Today) }
    var customStartDate by remember { mutableStateOf(salesRequest?.startDate ?: "") }
    var customEndDate by remember { mutableStateOf(salesRequest?.endDate ?: "") }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(top = 40.sdp, start = 16.sdp, end = 16.sdp),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        Text(text = "Select Date Range for Sales Report")

        Spacer(modifier = Modifier.height(8.dp))

        Column(
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(
                modifier = Modifier
                    .padding(16.dp),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                DateRangeDropdown(
                    selectedRange = selectedDateRange,
                    onRangeSelected = { startDate, endDate, range ->
                        selectedDateRange = range
                        customStartDate = startDate
                        customEndDate = endDate
                        updateSalesReport(startDate, endDate)
                    },
                    viewModel = viewModel
                )

                Spacer(modifier = Modifier.width(16.dp))

                TextButton(
                    onClick = {
                        onSalesReportClick()
                    }, modifier = Modifier
                        .background(color = Color(0xFF009551))
                        .width(100.dp)
                ) {
                    Text(text = "Sales Report", color = Color.White)
                }
            }
            
            // Display selected date range
            if (customStartDate.isNotEmpty() && customEndDate.isNotEmpty()) {
                val rangeText = if (customStartDate == customEndDate) {
                    customStartDate
                } else {
                    "$customStartDate to $customEndDate"
                }
                
                Text(
                    text = "Viewing: $rangeText",
                    fontSize = 14.sp,
                    fontStyle = FontStyle.Italic,
                    color = Color(0xFF009551),
                    modifier = Modifier.padding(bottom = 8.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        Card(
            modifier = Modifier.fillMaxWidth(), elevation = 4.dp
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(text = "Total Sales", style = MaterialTheme.typography.h5)
                Spacer(modifier = Modifier.height(8.dp))
                if (salesResponse is Loading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        color = Color(0xFF009551)
                    )
                } else {
                    Text(text = totalSales, style = MaterialTheme.typography.h4)
                }
            }
        }

        Card(
            modifier = Modifier.fillMaxWidth(), elevation = 4.dp
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(text = "Total Amount", style = MaterialTheme.typography.h5)
                Spacer(modifier = Modifier.height(8.dp))
                if (salesResponse is Loading) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        color = Color(0xFF009551)
                    )
                } else {
                    Text(
                        text = "£$totalAmount",
                        style = MaterialTheme.typography.h4
                    )
                }
            }
        }
    }
}

@Composable
fun SalesReportDialog(
    modifier: Modifier = Modifier,
    onPrintBill: (Bitmap) -> Unit,
    onCancel: () -> Unit,
    productsScreenViewModel: ProductsScreenViewModel
) {
    val salesReportResponse by productsScreenViewModel.collectAsState(ProductsScreenState::salesReportResponse)
    val shouldPrintInstant by productsScreenViewModel.collectAsState(ProductsScreenState::shouldPrintInstant)
    
    // Get the date range from the request
    val salesRequest by productsScreenViewModel.collectAsState(ProductsScreenState::salesRequest)
    val startDate = salesRequest?.startDate ?: ""
    val endDate = salesRequest?.endDate ?: ""
    val dateRangeText = if (startDate.isNotEmpty() && endDate.isNotEmpty()) {
        if (startDate == endDate) {
            startDate
        } else {
            "$startDate to $endDate"
        }
    } else {
        ""
    }

    val screenshot = screenshotableComposable(content = {
        Column(
            modifier = Modifier
                .width(400.dp)
                .background(color = Color.White)
                .padding(8.dp)
        ) {
            SalesReportPrintingBill(
                salesReportResponse = salesReportResponse() ?: SalesReportResponse(),
                productsScreenViewModel = productsScreenViewModel,
                prefs = productsScreenViewModel.prefs,
                dateRangeText = dateRangeText
            )
        }
    })

    Dialog(onDismissRequest = { onCancel() }) {
        androidx.compose.material3.Card(
            shape = RoundedCornerShape(10.dp),
            modifier = Modifier.padding(10.dp, 5.dp, 10.dp, 10.dp)
        ) {

            when (salesReportResponse) {
                is Loading -> {
                    Column(
                        modifier = Modifier.fillMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(color = Color.Blue)
                    }
                }

                else -> {
                    Column(
                        modifier
                            .background(Color.White)
                            .height(400.sdp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        // Show date range in the header
                        if (dateRangeText.isNotEmpty()) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .background(Color(0xFF009551))
                                    .padding(8.dp),
                                horizontalArrangement = Arrangement.Center
                            ) {
                                Text(
                                    text = "Sales Report: $dateRangeText",
                                    style = MaterialTheme.typography.h6,
                                    color = Color.White
                                )
                            }
                        }
                        
                        if (shouldPrintInstant) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.Center
                            ) {
                                CircularProgressIndicator(color = Color.Blue)
                            }
                            val bitmap = screenshot.invoke()
                            onPrintBill(bitmap)
                        }
                        screenshot()
                        Column(
                            modifier = Modifier
                                .width(400.dp)
                                .background(color = Color.White)
                                .padding(8.dp)
                                .weight(1f, fill = true)
                                .verticalScroll(rememberScrollState())
                                .verticalScrollbar(rememberScrollState())
                        ) {
                            SalesReportPrintingBill(
                                salesReportResponse = salesReportResponse()
                                    ?: SalesReportResponse(),
                                productsScreenViewModel = productsScreenViewModel,
                                prefs = productsScreenViewModel.prefs,
                                dateRangeText = dateRangeText
                            )
                        }
                        if (!shouldPrintInstant) {
                            Row(
                                Modifier
                                    .fillMaxWidth()
                                    .padding(top = 10.dp)
                                    .background(Color(0xFF009551)),
                                horizontalArrangement = Arrangement.SpaceAround
                            ) {
                                TextButton(modifier = Modifier.fillMaxWidth(0.5f), onClick = {
                                    onCancel()
                                }) {
                                    Text(
                                        "Don't Print",
                                        style = androidx.compose.material3.MaterialTheme.typography.titleLarge,
                                        color = Color.White,
                                        modifier = Modifier.padding(top = 5.dp, bottom = 5.dp)
                                    )
                                }
                                Divider(
                                    color = Color.White,
                                    modifier = Modifier
                                        .height(20.sdp)  //fill the max height
                                        .width(1.sdp)
                                )
                                TextButton(modifier = Modifier.fillMaxWidth(1f), onClick = {
                                    val bitmap = screenshot.invoke()
                                    onPrintBill(bitmap)
                                }) {
                                    Text(
                                        "Print Bill",
                                        style = androidx.compose.material3.MaterialTheme.typography.titleLarge,
                                        color = Color.Black,
                                        modifier = Modifier.padding(top = 5.dp, bottom = 5.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}


@Composable
fun SalesReportPrintingBill(
    salesReportResponse: SalesReportResponse, prefs: Prefs,
    productsScreenViewModel: ProductsScreenViewModel,
    dateRangeText : String
) {
    val now = productsScreenViewModel.trueTimeImpl.now()
    val order = salesReportResponse
    Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
        Text(
            text = "Sales Report",
            style = androidx.compose.material3.MaterialTheme.typography.headlineMedium
        )
    }
    Spacer(modifier = Modifier.padding(4.dp))
    Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
        Text(
            text = prefs.loginResponse?.businesses?.name ?: "",
            style = androidx.compose.material3.MaterialTheme.typography.headlineMedium
        )
    }
    Spacer(modifier = Modifier.padding(4.dp))
    DateComposable(
        title = "Date: ",
        value = dateRangeText,
        style = androidx.compose.material3.MaterialTheme.typography.headlineSmall
    )
    if(prefs.storeConfigurations?.data?.vatNumber?.isNotEmpty() == true) {
        Spacer(modifier = Modifier.padding(4.dp))
        DateComposable(
            title = "VAT Number: ",
            value = prefs.storeConfigurations?.data?.vatNumber ?: "",
            style = androidx.compose.material3.MaterialTheme.typography.headlineSmall
        )
    }
    MyTextDivider()
    Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
        Text(
            text = "Sales for Paid Bills",
            style = androidx.compose.material3.MaterialTheme.typography.headlineMedium
        )
    }
    Spacer(modifier = Modifier.padding(4.dp))
    /*order.categoryStats?.forEach { (key, value) ->
        Total(
            title = "${key} (${value.totalCount ?: 0})",
            value = "£ ${(value.totalPrice ?: 0.0).transformDecimal()}",
            style = androidx.compose.material3.MaterialTheme.typography.headlineSmall
        )
    }
    Spacer(modifier = Modifier.padding(4.dp))*/
    Total2(
        title = "Total Item Sales",
        value = "£${(order.itemSales)?.transformDecimal()}",
        style = androidx.compose.material3.MaterialTheme.typography.headlineSmall
    )
    Spacer(modifier = Modifier.padding(4.dp))
    val totalVatItems = order.vatItemsTotal ?: 0.0
    Total2(
        title = "Total VAT Items",
        value = "£${(totalVatItems).transformDecimal()}",
        style = androidx.compose.material3.MaterialTheme.typography.headlineSmall
    )
    Spacer(modifier = Modifier.padding(4.dp))
    Total2(
        title = "VAT",
        value = "£${(order.vat ?: 0.0).transformDecimal()}",
        style = androidx.compose.material3.MaterialTheme.typography.headlineSmall
    )
    Spacer(modifier = Modifier.padding(4.dp))
    var nonVatItems = if ((order.nonVatItemsTotal ?: 0.0) >= 0) {
        order.nonVatItemsTotal ?: 0.0
    } else {
        0.0
    }
    Total2(
        title = "Non VAT Items",
        value = "£${nonVatItems.transformDecimal()}",
        style = androidx.compose.material3.MaterialTheme.typography.headlineSmall
    )
    Spacer(modifier = Modifier.padding(4.dp))
    Total2(
        title = "Delivery Charge",
        value = "£${(order.deliveryCharge ?: 0.0).transformDecimal()}",
        style = androidx.compose.material3.MaterialTheme.typography.headlineSmall
    )
    Spacer(modifier = Modifier.padding(4.dp))
    Total2(
        title = "Net Sales",
        value = "£${(order.netSales ?: 0.0).transformDecimal()}",
        style = androidx.compose.material3.MaterialTheme.typography.headlineSmall
    )
    Spacer(modifier = Modifier.padding(4.dp))
    MyTextDivider()
    Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
        Text(
            text = "Payment",
            style = androidx.compose.material3.MaterialTheme.typography.headlineLarge
        )
    }
    Spacer(modifier = Modifier.padding(4.dp))
    Total2(
        title = "Card (${order.cardOrders ?: 0})",
        value = "£${(order.cardPayment ?: 0.0).transformDecimal()}",
        style = androidx.compose.material3.MaterialTheme.typography.headlineSmall
    )
    Spacer(modifier = Modifier.padding(4.dp))
    Total2(
        title = "Cash (${order.cashOrders ?: 0})",
        value = "£${(order.cashPayment ?: 0.0).transformDecimal()}",
        style = androidx.compose.material3.MaterialTheme.typography.headlineSmall
    )
    Spacer(modifier = Modifier.padding(4.dp))
    Spacer(modifier = Modifier.padding(4.dp))
    Total2(
        title = "Total Transactions",
        value = "${((order.cardOrders ?: 0) + (order.cashOrders ?: 0))}",
        style = androidx.compose.material3.MaterialTheme.typography.headlineSmall
    )
    MyTextDivider()

}


@Composable
fun AutoResizeText(
    text: String,
    fontSizeRange: FontSizeRange,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontStyle: FontStyle? = null,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
    letterSpacing: TextUnit = TextUnit.Unspecified,
    textDecoration: TextDecoration? = null,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    style: TextStyle = LocalTextStyle.current,
) {
    var fontSizeValue by remember { mutableStateOf(fontSizeRange.max.value) }
    var readyToDraw by remember { mutableStateOf(false) }

    Text(
        text = text,
        color = color,
        maxLines = maxLines,
        fontStyle = fontStyle,
        fontWeight = fontWeight,
        fontFamily = fontFamily,
        letterSpacing = letterSpacing,
        textDecoration = textDecoration,
        textAlign = textAlign,
        lineHeight = lineHeight,
        overflow = overflow,
        softWrap = softWrap,
        style = style,
        fontSize = fontSizeValue.sp,
        onTextLayout = {
            if (it.didOverflowHeight && !readyToDraw) {
                val nextFontSizeValue = fontSizeValue - fontSizeRange.step.value
                if (nextFontSizeValue <= fontSizeRange.min.value) {
                    // Reached minimum, set minimum font size and it's readToDraw
                    fontSizeValue = fontSizeRange.min.value
                    readyToDraw = true
                } else {
                    // Text doesn't fit yet and haven't reached minimum text range, keep decreasing
                    fontSizeValue = nextFontSizeValue
                }
            } else {
                // Text fits before reaching the minimum, it's readyToDraw
                readyToDraw = true
            }
        },
        modifier = modifier.drawWithContent { if (readyToDraw) drawContent() }
    )
}

@Composable
fun Total2(
    title: String,
    value: String,
    style: TextStyle = MaterialTheme.typography.h4,
    isBold: Boolean = false
) {
    val minSize = 12.sp
    val maxSize = 25.sp

    val minSize2 = 12.sp
    val maxSize2 = 20.sp
    Row(
        modifier = Modifier
            .width(400.dp)
            .padding(end = 10.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.Bottom
    ) {
        AutoResizeText(
            modifier = Modifier.weight(1f),
            text = title,
            style = style,
            fontSizeRange = FontSizeRange(min = minSize, max = maxSize),
            fontWeight = FontWeight.Bold
        )
        AutoResizeText(
            modifier = Modifier
                .weight(0.8f)
                .align(Alignment.Bottom), text = value, style = style,
            textAlign = TextAlign.End,
            fontSizeRange = FontSizeRange(min = minSize2, max = maxSize2),
            fontWeight = FontWeight.Bold
        )
    }
}

data class FontSizeRange(
    val min: TextUnit,
    val max: TextUnit,
    val step: TextUnit = DEFAULT_TEXT_STEP,
) {
    init {
        require(min < max) { "min should be less than max, $this" }
        require(step.value > 0) { "step should be greater than 0, $this" }
    }

    companion object {
        private val DEFAULT_TEXT_STEP = 1.sp
    }
}
